import { logging } from "@skywind-group/sw-utils";
import { loadDomains } from "../services/domainDetector/loadDomains";
import { CronJobFn } from "../utils/cronJob";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    const domains = await loadDomains();
    log.info(`Found ${domains.length} unique domains from all entities with domain detector pools`);
    for (const domain of domains) {
    }
}
