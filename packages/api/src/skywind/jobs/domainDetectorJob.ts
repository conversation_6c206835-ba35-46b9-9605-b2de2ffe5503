import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";
import { EntityStatus } from "../entities/entity";

const log = logging.logger("domain-detector-job");

interface DomainInfo {
    id: number;
    domain: string;
    source: Array<{
        model: string;
        id: number;
    }>;
}

async function loadAllDomains(): Promise<DomainInfo[]> {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "name", "domainDetectorAdapterId"],
                required: true,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        through: {
                            attributes: ["isActive"]
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.StaticDomainModel,
                as: "staticDomain",
                foreignKey: "staticDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "lobbyDomain",
                foreignKey: "lobbyDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "liveStreamingDomain",
                foreignKey: "liveStreamingDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "ehubDomain",
                foreignKey: "ehubDomainId",
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id", "name", "path", "type", "status", "staticDomainPoolId", "staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });

    // Extract domains from all entities and flatten into a single array
    const allDomains: DomainInfo[] = [];
    for (const entity of entities) {
        const entityDomains = extractDomainsFromEntity(entity as any);
        allDomains.push(...entityDomains);
    }

    // Remove duplicates based on domain field and merge sources
    const domainMap = new Map<string, DomainInfo>();

    for (const domain of allDomains) {
        if (domainMap.has(domain.domain)) {
            // Merge sources for existing domain
            const existing = domainMap.get(domain.domain)!;
            existing.source.push(...domain.source);
        } else {
            // Add new domain
            domainMap.set(domain.domain, domain);
        }
    }

    const uniqueDomains = Array.from(domainMap.values());

    return uniqueDomains;
}

function extractDomainsFromEntity(entity: any): DomainInfo[] {
        const allDomains: DomainInfo[] = [];
    
    const pool = entity.StaticDomainPoolModel;
    if (pool.domains && pool.domains.length > 0) {
        for (const domain of pool.domains) {
            const isActive = domain.StaticDomainPoolItem?.isActive ?? true;
            if (isActive) {
                allDomains.push({
                    id: domain.id,
                    domain: domain.domain,
                    source: [{
                        model: "StaticDomainPool",
                        id: pool.id
                    }]
                });
            }
        }
    }

    const entityId = entity.id;

    const staticDomain = entity.staticDomain;
    if (staticDomain) {
        allDomains.push({
            id: staticDomain.id,
            domain: staticDomain.domain,
            source: [{
                model: "Entity",
                id: entityId
            }]
        });
    }

    const lobbyDomain = entity.lobbyDomain;
    if (lobbyDomain) {
        allDomains.push({
            id: lobbyDomain.id,
            domain: lobbyDomain.domain,
            source: [{
                model: "Entity",
                id: entityId
            }]
        });
    }

    const liveStreamingDomain = entity.liveStreamingDomain;
    if (liveStreamingDomain) {
        allDomains.push({
            id: liveStreamingDomain.id,
            domain: liveStreamingDomain.domain,
            source: [{
                model: "Entity",
                id: entityId
            }]
        });
    }

    const ehubDomain = entity.ehubDomain;
    if (ehubDomain) {
        allDomains.push({
            id: ehubDomain.id,
            domain: ehubDomain.domain,
            source: [{
                model: "Entity",
                id: entityId
            }]
        });
    }

    return allDomains;
}

export const domainDetectorJob: CronJobFn = async () => {
    // Load all unique domains from all entities
    const allDomains = await loadAllDomains();

    log.info(`Found ${allDomains.length} unique domains from all entities with domain detector pools`);

    for (const domain of allDomains) {
        const sources = domain.source.map(s => `${s.model}:${s.id}`).join(", ");
        log.info(`Processing domain: ${domain.domain} (ID: ${domain.id}, Sources: [${sources}])`);

        // TODO: Add domain detector processing logic for each unique domain here
    }
}
