import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";
import { EntityStatus } from "../entities/entity";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "name", "domainDetectorAdapterId"],
                required: true
            }
        ],
        attributes: ["staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });

    log.info(`Found ${entities.length} entities with domain detector pools`);

    for (const entity of entities) {
        const pool = (entity as any).StaticDomainPoolModel;
        log.info(`Processing entity: ${entity.name} (ID: ${entity.id}, Path: ${entity.path}) with pool: ${pool.name} (ID: ${pool.id}) using adapter: ${pool.domainDetectorAdapterId}`);
        // TODO: Add domain detector processing logic for each entity here
    }
}
