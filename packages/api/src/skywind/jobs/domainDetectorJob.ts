import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    try {
        log.info("Starting domain detector job");

        // Find all entities with non-empty staticDomainPoolId that have static domain pools with non-empty domainDetectorAdapterId
        const entitiesWithDetectorPools = await Models.EntityModel.findAll({
            where: {
                staticDomainPoolId: {
                    [Op.ne]: null
                }
            },
            include: [
                {
                    model: Models.StaticDomainPoolModel,
                    on: {
                        id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                    },
                    where: {
                        domainDetectorAdapterId: {
                            [Op.and]: [
                                { [Op.ne]: null },
                                { [Op.ne]: "" }
                            ]
                        }
                    },
                    attributes: ["id", "name", "domainDetectorAdapterId"],
                    required: true
                }
            ],
            attributes: ["id", "name", "path", "type", "status", "staticDomainPoolId"]
        });

        log.info(`Found ${entitiesWithDetectorPools.length} entities with domain detector pools`);

        for (const entity of entitiesWithDetectorPools) {
            const pool = (entity as any).StaticDomainPoolModel;
            log.info(`Processing entity: ${entity.name} (ID: ${entity.id}, Path: ${entity.path}) with pool: ${pool.name} (ID: ${pool.id}) using adapter: ${pool.domainDetectorAdapterId}`);
            // TODO: Add domain detector processing logic for each entity here
        }

        log.info("Domain detector job completed successfully");
    } catch (error) {
        log.error("Error in domain detector job:", error);
        throw error;
    }
}
