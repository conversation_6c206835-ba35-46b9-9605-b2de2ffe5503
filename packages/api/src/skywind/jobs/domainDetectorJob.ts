import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    try {
        log.info("Starting domain detector job");

        // First, get all static domain pool IDs that have non-empty domainDetectorAdapterId
        const poolsWithDetector = await Models.StaticDomainPoolModel.findAll({
            where: {
                domainDetectorAdapterId: {
                    [Op.and]: [
                        { [Op.ne]: null },
                        { [Op.ne]: "" }
                    ]
                }
            },
            attributes: ["id", "name", "domainDetectorAdapterId"]
        });

        const poolIds = poolsWithDetector.map(pool => pool.id);

        if (poolIds.length === 0) {
            log.info("No static domain pools found with domain detector adapters");
            return;
        }

        // Find all entities that use these static domain pools
        const entitiesWithDetectorPools = await Models.EntityModel.findAll({
            where: {
                staticDomainPoolId: {
                    [Op.in]: poolIds
                }
            },
            attributes: ["id", "name", "path", "type", "status", "staticDomainPoolId"]
        });

        log.info(`Found ${entitiesWithDetectorPools.length} entities with domain detector pools`);

        // Create a map for quick pool lookup
        const poolMap = new Map(poolsWithDetector.map(pool => [pool.id, pool]));

        for (const entity of entitiesWithDetectorPools) {
            const pool = poolMap.get(entity.staticDomainPoolId);
            log.info(`Processing entity: ${entity.name} (ID: ${entity.id}, Path: ${entity.path}) with pool: ${pool.name} (ID: ${pool.id}) using adapter: ${pool.domainDetectorAdapterId}`);
            // TODO: Add domain detector processing logic for each entity here
        }

        log.info("Domain detector job completed successfully");
    } catch (error) {
        log.error("Error in domain detector job:", error);
        throw error;
    }
}
