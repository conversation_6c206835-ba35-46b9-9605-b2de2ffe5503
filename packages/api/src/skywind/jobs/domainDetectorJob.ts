import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";
import { EntityStatus } from "../entities/entity";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "name", "domainDetectorAdapterId"],
                required: true,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        through: {
                            attributes: ["isActive"]
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.StaticDomainModel,
                as: "staticDomain",
                foreignKey: "staticDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "lobbyDomain",
                foreignKey: "lobbyDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "liveStreamingDomain",
                foreignKey: "liveStreamingDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "ehubDomain",
                foreignKey: "ehubDomainId",
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id", "name", "path", "type", "status", "staticDomainPoolId", "staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });

    log.info(`Found ${entities.length} entities with domain detector pools`);

    for (const entity of entities) {
        const pool = (entity as any).StaticDomainPoolModel;
        const staticDomain = (entity as any).staticDomain;
        const lobbyDomain = (entity as any).lobbyDomain;
        const liveStreamingDomain = (entity as any).liveStreamingDomain;
        const ehubDomain = (entity as any).ehubDomain;

        log.info(`Processing entity: ${entity.name} (ID: ${entity.id}, Path: ${entity.path}) with pool: ${pool.name} (ID: ${pool.id}) using adapter: ${pool.domainDetectorAdapterId}`);

        // Log pool domains
        if (pool.domains && pool.domains.length > 0) {
            log.info(`  Pool domains (${pool.domains.length}):`);
            for (const domain of pool.domains) {
                const isActive = domain.StaticDomainPoolItem?.isActive ?? true;
                log.info(`    - ${domain.domain} (ID: ${domain.id}, Active: ${isActive})`);
            }
        } else {
            log.info("  Pool has no domains");
        }

        // Log entity-specific domains
        if (staticDomain) {
            log.info(`  Static Domain: ${staticDomain.domain} (ID: ${staticDomain.id}, Type: ${staticDomain.type})`);
        }
        if (lobbyDomain) {
            log.info(`  Lobby Domain: ${lobbyDomain.domain} (ID: ${lobbyDomain.id}, Type: ${lobbyDomain.type})`);
        }
        if (liveStreamingDomain) {
            log.info(`  Live Streaming Domain: ${liveStreamingDomain.domain} (ID: ${liveStreamingDomain.id}, Type: ${liveStreamingDomain.type})`);
        }
        if (ehubDomain) {
            log.info(`  eHub Domain: ${ehubDomain.domain} (ID: ${ehubDomain.id}, Type: ${ehubDomain.type})`);
        }

        // TODO: Add domain detector processing logic for each entity here
    }
}
