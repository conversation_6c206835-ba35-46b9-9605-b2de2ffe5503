import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";
import { EntityStatus } from "../entities/entity";

const log = logging.logger("domain-detector-job");

interface DomainInfo {
    id: number;
    domain: string;
    source: string;
}

async function loadAllDomains(): Promise<DomainInfo[]> {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "name", "domainDetectorAdapterId"],
                required: true,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        through: {
                            attributes: ["isActive"]
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.StaticDomainModel,
                as: "staticDomain",
                foreignKey: "staticDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "lobbyDomain",
                foreignKey: "lobbyDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "liveStreamingDomain",
                foreignKey: "liveStreamingDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "ehubDomain",
                foreignKey: "ehubDomainId",
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id", "name", "path", "type", "status", "staticDomainPoolId", "staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });

    // Extract domains from all entities and flatten into a single array
    const allDomains: DomainInfo[] = [];
    for (const entity of entities) {
        const entityDomains = extractDomainsFromEntity(entity as any);
        allDomains.push(...entityDomains);
    }

    // Remove duplicates based on domain field across all entities
    const uniqueDomains = allDomains.filter((domain, index, self) =>
        index === self.findIndex(d => d.domain === domain.domain)
    );

    return uniqueDomains;
}

function extractDomainsFromEntity(entity: any): DomainInfo[] {
    const pool = entity.StaticDomainPoolModel;
    const staticDomain = entity.staticDomain;
    const lobbyDomain = entity.lobbyDomain;
    const liveStreamingDomain = entity.liveStreamingDomain;
    const ehubDomain = entity.ehubDomain;

    const allDomains: DomainInfo[] = [];

    // Add pool domains (only active ones)
    if (pool.domains && pool.domains.length > 0) {
        for (const domain of pool.domains) {
            const isActive = domain.StaticDomainPoolItem?.isActive ?? true;
            if (isActive) {
                allDomains.push({
                    id: domain.id,
                    domain: domain.domain,
                    source: `pool:${pool.name}`
                });
            }
        }
    }

    // Add entity-specific domains
    if (staticDomain) {
        allDomains.push({
            id: staticDomain.id,
            domain: staticDomain.domain,
            source: "entity:static"
        });
    }
    if (lobbyDomain) {
        allDomains.push({
            id: lobbyDomain.id,
            domain: lobbyDomain.domain,
            source: "entity:lobby"
        });
    }
    if (liveStreamingDomain) {
        allDomains.push({
            id: liveStreamingDomain.id,
            domain: liveStreamingDomain.domain,
            source: "entity:liveStreaming"
        });
    }
    if (ehubDomain) {
        allDomains.push({
            id: ehubDomain.id,
            domain: ehubDomain.domain,
            source: "entity:ehub"
        });
    }

    // Remove duplicates based on domain field
    const uniqueDomains = allDomains.filter((domain, index, self) =>
        index === self.findIndex(d => d.domain === domain.domain)
    );

    return uniqueDomains;
}

export const domainDetectorJob: CronJobFn = async () => {
    // Load all entities with their domains
    const entitiesWithDomains = await loadAllDomains();

    log.info(`Found ${entitiesWithDomains.length} entities with domain detector pools`);

    for (const { entity, domains } of entitiesWithDomains) {
        const pool = entity.StaticDomainPoolModel;

        log.info(`Processing entity: ${entity.name} (ID: ${entity.id}, Path: ${entity.path}) with pool: ${pool.name} (ID: ${pool.id}) using adapter: ${pool.domainDetectorAdapterId}`);

        log.info(`  Found ${domains.length} unique domains:`);
        for (const domain of domains) {
            log.info(`    - ${domain.domain} (ID: ${domain.id}, Source: ${domain.source})`);
        }

        // TODO: Add domain detector processing logic for each unique domain here
    }
}
