import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { getStaticDomainPoolService } from "../services/staticDomainPool";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    try {
        log.info("Starting domain detector job");

        // Load all domain static pools with non-empty domainDetectorAdapterId
        const staticDomainPoolService = getStaticDomainPoolService();
        const poolsWithDetector = await staticDomainPoolService.findAll({
            where: {
                domainDetectorAdapterId: {
                    [Op.and]: [
                        { [Op.ne]: null },
                        { [Op.ne]: "" }
                    ]
                }
            }
        });

        log.info(`Found ${poolsWithDetector.length} static domain pools with domain detector adapter`);

        for (const pool of poolsWithDetector) {
            log.info(`Processing pool: ${pool.name} (ID: ${pool.id}) with adapter: ${pool.domainDetectorAdapterId}`);
            // TODO: Add domain detector processing logic here
        }

        log.info("Domain detector job completed successfully");
    } catch (error) {
        log.error("Error in domain detector job:", error);
        throw error;
    }
}
