import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { Models } from "../models/models";
import { EntityStatus } from "../entities/entity";

const log = logging.logger("domain-detector-job");

interface DomainSource {
    model: string;
    id: number;
}

interface DomainInfo {
    id: number;
    domain: string;
    source: DomainSource[];
}

async function loadAllDomains(): Promise<DomainInfo[]> {
    const entities = await Models.EntityModel.findAll({
        where: {
            staticDomainPoolId: {
                [Op.ne]: null
            },
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                on: {
                    id: { [Op.col]: "EntityModel.staticDomainPoolId" }
                },
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id"],
                required: true,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        through: {
                            attributes: ["isActive"]
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.StaticDomainModel,
                as: "staticDomain",
                foreignKey: "staticDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "lobbyDomain",
                foreignKey: "lobbyDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "liveStreamingDomain",
                foreignKey: "liveStreamingDomainId",
                attributes: ["id", "domain"],
                required: false
            },
            {
                model: Models.StaticDomainModel,
                as: "ehubDomain",
                foreignKey: "ehubDomainId",
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id", "staticDomainPoolId", "staticDomainId", "lobbyDomainId", "liveStreamingDomainId", "ehubDomainId"]
    });

    const items = new Map<string, DomainInfo>();
    for (const entity of entities) {
        toDomains(entity as any, items);
    }
    return Array.from(items.values());
}

function toDomains(entity: any, items: Map<string, DomainInfo>): void {
    const pool = entity.StaticDomainPoolModel;
    const entityId = entity.id;

    const addDomain = (id: number, domain: string, source: DomainSource) => {
        items.set(domain, {
            id,
            domain,
            source: [source, ...items.get(domain)?.source ?? []]
        });
    };

    if (pool.domains && pool.domains.length > 0) {
        for (const domain of pool.domains) {
            const isActive = domain.StaticDomainPoolItem?.isActive ?? true;
            if (isActive) {
                addDomain(domain.id, domain.domain, {
                    model: "StaticDomainPool",
                    id: pool.id
                });
            }
        }
    }

    const staticDomain = entity.staticDomain;
    if (staticDomain) {
        addDomain(staticDomain.id, staticDomain.domain, {
            model: "Entity",
            id: entityId
        });
    }

    const lobbyDomain = entity.lobbyDomain;
    if (lobbyDomain) {
        addDomain(lobbyDomain.id, lobbyDomain.domain, {
            model: "Entity",
            id: entityId
        });
    }

    const liveStreamingDomain = entity.liveStreamingDomain;
    if (liveStreamingDomain) {
        addDomain(liveStreamingDomain.id, liveStreamingDomain.domain, {
            model: "Entity",
            id: entityId
        });
    }

    const ehubDomain = entity.ehubDomain;
    if (ehubDomain) {
        addDomain(ehubDomain.id, ehubDomain.domain, {
            model: "Entity",
            id: entityId
        });
    }
}

export const domainDetectorJob: CronJobFn = async () => {
    // Load all unique domains from all entities
    const allDomains = await loadAllDomains();

    log.info(`Found ${allDomains.length} unique domains from all entities with domain detector pools`);

    for (const domain of allDomains) {
        const sources = domain.source.map(s => `${s.model}:${s.id}`).join(", ");
        log.info(`Processing domain: ${domain.domain} (ID: ${domain.id}, Sources: [${sources}])`);

        // TODO: Add domain detector processing logic for each unique domain here
    }
}
