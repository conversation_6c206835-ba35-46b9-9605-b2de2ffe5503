import { logging } from "@skywind-group/sw-utils";
import { Op } from "sequelize";
import { CronJobFn } from "../utils/cronJob";
import { getStaticDomainPoolService } from "../services/staticDomainPool";
import { Models } from "../models/models";

const log = logging.logger("domain-detector-job");

export const domainDetectorJob: CronJobFn = async () => {
    try {
        log.info("Starting domain detector job");

        // Load all domain static pools with non-empty domainDetectorAdapterId
        const staticDomainPoolService = getStaticDomainPoolService();
        const poolsWithDetector = await staticDomainPoolService.findAll({
            where: {
                domainDetectorAdapterId: {
                    [Op.and]: [
                        { [Op.ne]: null },
                        { [Op.ne]: "" }
                    ]
                }
            }
        });

        log.info(`Found ${poolsWithDetector.length} static domain pools with domain detector adapter`);

        for (const pool of poolsWithDetector) {
            log.info(`Processing pool: ${pool.name} (ID: ${pool.id}) with adapter: ${pool.domainDetectorAdapterId}`);

            // Find all entities that use this static domain pool
            const relatedEntities = await Models.EntityModel.findAll({
                where: {
                    staticDomainPoolId: pool.id
                },
                attributes: ['id', 'name', 'path', 'type', 'status']
            });

            log.info(`Found ${relatedEntities.length} entities using pool ${pool.name}`);

            for (const entity of relatedEntities) {
                log.info(`  - Entity: ${entity.name} (ID: ${entity.id}, Path: ${entity.path}, Type: ${entity.type}, Status: ${entity.status})`);
                // TODO: Add domain detector processing logic for each entity here
            }
        }

        log.info("Domain detector job completed successfully");
    } catch (error) {
        log.error("Error in domain detector job:", error);
        throw error;
    }
}
