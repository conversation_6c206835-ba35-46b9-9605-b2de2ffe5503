import { DynamicDomain, StaticDomain } from "./domain";
import { FindOptions } from "sequelize";

export interface StaticDomainPoolItemAttributes {
    isActive?: boolean;
    staticDomainId?: number;
    staticDomainPoolId?: number;
}

export interface DynamicDomainPoolItemAttributes {
    isActive?: boolean;
    dynamicDomainId?: number;
    dynamicDomainPoolId?: number;
}

interface DomainPoolService<T, U> {
    create(data: DomainPoolCreateData): Promise<T>;

    update(id: number, data: Partial<DomainPoolCreateData>): Promise<T>;

    findById(id: number): Promise<T>;

    findAll(findOptions?: FindOptions<T>): Promise<T[]>;

    remove(id: number): Promise<void>;

    addDomain(poolId: number, domainId: number): Promise<U>;

    removeDomain(poolId: number, domainId: number): Promise<void>;

    enableDomain(poolId: number, domainId: number): Promise<void>;

    disableDomain(poolId: number, domainId: number): Promise<void>;
}

export type StaticDomainPoolService = DomainPoolService<StaticDomainPoolAttributes, StaticDomainPoolItemAttributes>;
export type DynamicDomainPoolService = DomainPoolService<DynamicDomainPoolAttributes, DynamicDomainPoolItemAttributes>;

export interface ExtendedStaticDomain extends StaticDomain {
    isActive?: boolean;
    StaticDomainPoolItem?: { // connection table reference
        isActive?: boolean;
    };
}

export interface ExtendedDynamicDomain extends DynamicDomain {
    isActive?: boolean;
    DynamicDomainPoolItem?: { // connection table reference
        isActive?: boolean;
    };
}

export interface StaticDomainPoolAttributes {
    id?: number;
    name: string;
    inherited?: boolean;
    domainDetectorAdapterId?: string;
    domains?: ExtendedStaticDomain[];
    createdAt?: Date;
    updatedAt?: Date;
}

export interface DynamicDomainPoolAttributes {
    id?: number;
    name: string;
    domains?: ExtendedDynamicDomain[];
    createdAt?: Date;
    updatedAt?: Date;
}

export interface DomainPoolCreateData {
    name: string;
    domainDetectorAdapterId?: string;
    domains: { id: number; isActive?: boolean; environment?: string; }[];
}
